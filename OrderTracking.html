<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Tracking System</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>

    <style>
        /* Custom styles */
        body {
            font-family: sans-serif; /* Using default system font */
            background-color: #f0f2f5; /* A very light grey for contrast */
        }

        /* Progress bar line */
        .progress-line {
            position: absolute;
            height: 2px;
            background-color: #e5e7eb; /* gray-200 */
            top: 50%;
            left: 0;
            right: 0;
            transform: translateY(-50%);
            z-index: -1;
        }
        
        .progress-line-active {
             background-color: #000000; /* black */
        }
        
        /* Theming for black and white */
        .card {
            background-color: white;
            border: 1px solid #e5e7eb; /* gray-200 */
        }
        
        .dark-card {
             background-color: black;
             color: white;
        }

    </style>
</head>
<body class="antialiased">

    <div class="container mx-auto p-4 md:p-8 max-w-6xl">

        <!-- Header -->
        <header class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-black">Track Your Order</h1>
            <div class="flex items-center space-x-4">
                 <div class="relative">
                    <i data-lucide="bell" class="w-6 h-6 text-black"></i>
                    <span class="absolute -top-1 -right-1 w-4 h-4 bg-black text-white text-xs rounded-full flex items-center justify-center">2</span>
                </div>
                <i data-lucide="user" class="w-6 h-6 text-black"></i>
            </div>
        </header>

        <main class="grid grid-cols-1 lg:grid-cols-3 gap-8">

            <!-- Main Content: Left Column -->
            <div class="lg:col-span-2 space-y-8">
                
                <!-- Order Details Card -->
                <div class="card p-6 rounded-lg shadow-sm">
                    <div class="flex flex-col md:flex-row justify-between md:items-center gap-4">
                        <div>
                            <p class="text-gray-500">Order ID</p>
                            <p class="text-black font-semibold text-lg">#A45B-2024-N781</p>
                        </div>
                        <div class="text-left md:text-right">
                            <p class="text-gray-500">Estimated Delivery</p>
                            <p class="text-black font-semibold text-lg">June 9, 2025</p>
                        </div>
                        <button class="w-full md:w-auto bg-black text-white font-semibold py-2 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-800 transition-colors">
                            <i data-lucide="file-text" class="w-4 h-4"></i>
                            View Invoice
                        </button>
                    </div>
                </div>

                <!-- Order Status Progress Bar -->
                <div id="order-status-tracker" class="card p-6 rounded-lg shadow-sm">
                    <div class="relative w-full">
                       <div class="progress-line"></div>
                       <div id="progress-line-active" class="progress-line progress-line-active" style="width: 75%;"></div>
                        <div class="flex justify-between items-start">
                            <!-- Step 1: Placed -->
                            <div class="step text-center w-1/5" data-status="placed">
                                <div class="icon-bg mx-auto w-12 h-12 rounded-full border-2 flex items-center justify-center bg-black border-black">
                                    <i data-lucide="package-check" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="font-semibold mt-2 text-sm">Placed</p>
                            </div>
                             <!-- Step 2: Processed -->
                            <div class="step text-center w-1/5" data-status="processed">
                                <div class="icon-bg mx-auto w-12 h-12 rounded-full border-2 flex items-center justify-center bg-black border-black">
                                    <i data-lucide="settings-2" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="font-semibold mt-2 text-sm">Processed</p>
                            </div>
                            <!-- Step 3: Shipped -->
                            <div class="step text-center w-1/5" data-status="shipped">
                               <div class="icon-bg mx-auto w-12 h-12 rounded-full border-2 flex items-center justify-center bg-black border-black">
                                    <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="font-semibold mt-2 text-sm">Shipped</p>
                            </div>
                            <!-- Step 4: Out for Delivery -->
                             <div class="step text-center w-1/5" data-status="out-for-delivery">
                                <div class="icon-bg mx-auto w-12 h-12 rounded-full border-2 flex items-center justify-center bg-black border-black">
                                    <i data-lucide="map-pin" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="font-semibold mt-2 text-sm">On The Way</p>
                            </div>
                            <!-- Step 5: Delivered -->
                            <div class="step text-center w-1/5" data-status="delivered">
                                <div class="icon-bg mx-auto w-12 h-12 rounded-full border-2 flex items-center justify-center bg-white border-gray-300">
                                    <i data-lucide="home" class="w-6 h-6 text-gray-400"></i>
                                </div>
                                <p class="font-semibold mt-2 text-sm text-gray-400">Delivered</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Map and Delivery Agent -->
                <div class="grid grid-cols-1 md:grid-cols-5 gap-8">
                    <div class="md:col-span-3 card rounded-lg shadow-sm overflow-hidden h-96 md:h-auto">
                        <!-- Map Placeholder -->
                        <div class="h-full bg-gray-200 relative flex items-center justify-center">
                            <img src="https://placehold.co/600x400/e2e8f0/333333?text=Live+Map+View" class="w-full h-full object-cover" alt="Map Placeholder" onerror="this.onerror=null;this.src='https://placehold.co/600x400/e2e8f0/333333?text=Map+Error';">
                             <div class="absolute top-1/2 left-1/4 animate-pulse">
                                <i data-lucide="truck" class="w-8 h-8 text-black"></i>
                             </div>
                        </div>
                    </div>
                    <div class="md:col-span-2 flex flex-col gap-8">
                        <!-- Delivery Agent -->
                        <div class="card p-6 rounded-lg shadow-sm flex-grow">
                             <h3 class="font-bold text-lg mb-4">Delivery Agent</h3>
                             <div class="flex items-center space-x-4">
                                 <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                                      <i data-lucide="user-circle" class="w-8 h-8 text-black"></i>
                                 </div>
                                 <div>
                                     <p class="font-semibold text-black">John Doe</p>
                                     <p class="text-sm text-gray-500">Authorized Agent</p>
                                 </div>
                             </div>
                             <button class="mt-4 w-full bg-white border border-black text-black font-semibold py-2 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-100 transition-colors">
                                 <i data-lucide="phone" class="w-4 h-4"></i>
                                 Contact
                             </button>
                        </div>
                         <!-- Proof of Delivery -->
                        <div class="dark-card p-6 rounded-lg shadow-sm flex-grow">
                             <h3 class="font-bold text-lg mb-4">Proof of Delivery</h3>
                             <div class="flex items-center justify-center h-full">
                                 <div class="text-center">
                                      <i data-lucide="scan-line" class="w-10 h-10 text-white mx-auto"></i>
                                      <p class="mt-2 text-sm text-gray-300">Awaiting Delivery</p>
                                 </div>
                             </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Side Content: Right Column -->
            <div class="lg:col-span-1 space-y-8">
                <!-- Notifications/Updates -->
                <div class="card p-6 rounded-lg shadow-sm">
                    <h3 class="font-bold text-lg mb-4">Latest Updates</h3>
                    <ul class="space-y-4">
                        <li class="flex items-start space-x-4">
                            <div class="w-8 h-8 rounded-full bg-black flex-shrink-0 flex items-center justify-center">
                                <i data-lucide="map-pin" class="w-4 h-4 text-white"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-sm">Your order is out for delivery.</p>
                                <p class="text-xs text-gray-500">15 mins ago</p>
                            </div>
                        </li>
                         <li class="flex items-start space-x-4">
                            <div class="w-8 h-8 rounded-full bg-black flex-shrink-0 flex items-center justify-center">
                                <i data-lucide="truck" class="w-4 h-4 text-white"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-sm">Package has been shipped from the hub.</p>
                                <p class="text-xs text-gray-500">2 hours ago</p>
                            </div>
                        </li>
                         <li class="flex items-start space-x-4">
                            <div class="w-8 h-8 rounded-full bg-black flex-shrink-0 flex items-center justify-center">
                                <i data-lucide="settings-2" class="w-4 h-4 text-white"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-sm">Order is being processed.</p>
                                <p class="text-xs text-gray-500">1 day ago</p>
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- Order History -->
                <div class="card p-6 rounded-lg shadow-sm">
                    <h3 class="font-bold text-lg mb-4">Order History</h3>
                     <ul class="space-y-3">
                        <li class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <div class="flex items-center space-x-3">
                                <i data-lucide="check-circle" class="w-5 h-5 text-black"></i>
                                <div>
                                    <p class="font-semibold text-sm">#C12A-2024-N555</p>
                                    <p class="text-xs text-gray-500">Delivered on May 28, 2025</p>
                                </div>
                            </div>
                             <button class="bg-black text-white text-xs font-bold py-1 px-3 rounded-full hover:bg-gray-800">Re-order</button>
                        </li>
                         <li class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <div class="flex items-center space-x-3">
                                <i data-lucide="check-circle" class="w-5 h-5 text-black"></i>
                                <div>
                                    <p class="font-semibold text-sm">#F89B-2024-N431</p>
                                    <p class="text-xs text-gray-500">Delivered on April 15, 2025</p>
                                </div>
                            </div>
                             <button class="bg-black text-white text-xs font-bold py-1 px-3 rounded-full hover:bg-gray-800">Re-order</button>
                        </li>
                         <li class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <div class="flex items-center space-x-3">
                                <i data-lucide="x-circle" class="w-5 h-5 text-gray-400"></i>
                                <div>
                                    <p class="font-semibold text-sm">#D05E-2024-N102</p>
                                    <p class="text-xs text-gray-500">Cancelled on Mar 02, 2025</p>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

            </div>
        </main>

    </div>

    <script>
        // Use window.onload to ensure all content (including scripts, images, etc.) is fully loaded before executing the script.
        // This is a more robust way to prevent race conditions with external libraries.
        window.onload = function() {
            // Initialize Lucide Icons. This call finds all elements with `data-lucide` attributes and replaces them with SVG icons.
            lucide.createIcons();

            // This object would typically come from an API.
            // You can change the 'status' value to see the UI update.
            // Possible values: 'placed', 'processed', 'shipped', 'out-for-delivery', 'delivered'
            const orderData = {
                id: '#A45B-2024-N781',
                estimatedDelivery: 'June 9, 2025',
                status: 'out-for-delivery',
                agent: {
                    name: 'John Doe'
                },
                proofOfDelivery: null // Becomes something like { type: 'signature', url: '...' } upon delivery
            };

            // Defines the order of statuses for the progress bar calculation.
            const statusHierarchy = ['placed', 'processed', 'shipped', 'out-for-delivery', 'delivered'];

            /**
             * Updates the visual progress bar based on the current order status.
             * @param {string} currentStatus - The current status of the order.
             */
            function updateProgressBar(currentStatus) {
                const steps = document.querySelectorAll('#order-status-tracker .step');
                const activeLine = document.getElementById('progress-line-active');
                
                const currentIndex = statusHierarchy.indexOf(currentStatus);
                // Exit if the status is not found in our hierarchy.
                if (currentIndex === -1) {
                    console.error("Invalid order status:", currentStatus);
                    return;
                }

                // Loop through each step in the progress bar.
                steps.forEach((step, index) => {
                    const iconBg = step.querySelector('.icon-bg');
                    // Lucide replaces the <i> tag with an <svg> tag. We must query for the SVG tag now.
                    const icon = iconBg ? iconBg.querySelector('svg') : null;
                    const label = step.querySelector('p');
                    
                    // This defensive check prevents the script from crashing if any element is unexpectedly missing.
                    if (!iconBg || !icon || !label) {
                        console.error("Skipping a progress step because a required element was not found.", { step, index });
                        return; // 'continue' to the next iteration of the loop.
                    }
                    
                    // If the step's index is less than or equal to the current status's index, it's considered 'active' or 'completed'.
                    if (index <= currentIndex) {
                        iconBg.classList.remove('bg-white', 'border-gray-300');
                        iconBg.classList.add('bg-black', 'border-black');

                        // Lucide SVGs use `currentColor` for their stroke, so changing the text color class works to color the icon.
                        icon.classList.remove('text-gray-400');
                        icon.classList.add('text-white');
                        
                        label.classList.remove('text-gray-400');
                    } else {
                        // Otherwise, it's an 'inactive' future step.
                        iconBg.classList.remove('bg-black', 'border-black');
                        iconBg.classList.add('bg-white', 'border-gray-300');
                        
                        icon.classList.remove('text-white');
                        icon.classList.add('text-gray-400');
                        
                        label.classList.add('text-gray-400');
                    }
                });

                // Calculate and set the width of the active progress line.
                const percentage = (currentIndex / (statusHierarchy.length - 1)) * 100;
                // A check to prevent error if activeLine is not found
                if(activeLine) {
                    activeLine.style.width = `${percentage}%`;
                }
            }

            // Make the initial call to set the UI to the correct state based on our mock data.
            updateProgressBar(orderData.status);
        };

    </script>
</body>
</html>
