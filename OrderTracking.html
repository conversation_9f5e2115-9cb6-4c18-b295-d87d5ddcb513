<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Tracking System</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Leaflet CSS and JS for Interactive Maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

    <!-- Babel for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <style>
        /* Custom styles */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Enhanced animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes slideIn {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-pulse-custom {
            animation: pulse 2s infinite;
        }

        .animate-slide-in {
            animation: slideIn 0.5s ease-out;
        }

        /* Progress bar line */
        .progress-line {
            position: absolute;
            height: 4px;
            background: linear-gradient(90deg, #e5e7eb 0%, #d1d5db 100%);
            top: 50%;
            left: 0;
            right: 0;
            transform: translateY(-50%);
            z-index: -1;
            border-radius: 2px;
        }

        .progress-line-active {
            background: linear-gradient(90deg, #000000 0%, #374151 100%);
            transition: width 1s ease-in-out;
            border-radius: 2px;
        }

        /* Enhanced card styles */
        .card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(229, 231, 235, 0.5);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .dark-card {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: white;
            transition: all 0.3s ease;
        }

        .dark-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        /* Button enhancements */
        .btn-primary {
            background: linear-gradient(135deg, #000000 0%, #374151 100%);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        /* Loading skeleton */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        /* Status step enhancements */
        .step {
            transition: all 0.3s ease;
        }

        .step:hover {
            transform: scale(1.05);
        }

        .step .icon-bg {
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-left: 4px solid #000;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateX(0);
        }

        /* Search input styles */
        .search-input {
            transition: all 0.3s ease;
        }

        .search-input:focus {
            transform: scale(1.02);
            box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        }

        /* Theme toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }

        /* Dark theme */
        .dark-theme {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            color: white;
        }

        .dark-theme .card {
            background: rgba(31, 41, 55, 0.95);
            border-color: rgba(75, 85, 99, 0.5);
        }

        .dark-theme .progress-line {
            background: linear-gradient(90deg, #374151 0%, #4b5563 100%);
        }

        /* Map container fixes */
        .map-container {
            height: 400px;
            width: 100%;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }

        .leaflet-container {
            height: 100%;
            width: 100%;
            border-radius: 8px;
        }

        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .modal.show .modal-content {
            transform: scale(1);
        }

        /* Order item animations */
        .order-item-clickable {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .order-item-clickable:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Progress bar animation fix */
        .progress-line-active {
            transition: width 2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Delivery truck animation */
        @keyframes moveDeliveryTruck {
            0% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(10px) translateY(-5px); }
            50% { transform: translateX(20px) translateY(0); }
            75% { transform: translateX(15px) translateY(5px); }
            100% { transform: translateX(25px) translateY(0); }
        }

        .delivery-truck-moving {
            animation: moveDeliveryTruck 4s ease-in-out infinite;
        }

        /* Action button styles */
        .action-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .action-btn.danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .action-btn.warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

    </style>
</head>
<body class="antialiased">
    <!-- Theme Toggle -->
    <button id="theme-toggle" class="theme-toggle btn-primary text-white p-3 rounded-full">
        <i data-lucide="moon" class="w-5 h-5"></i>
    </button>

    <!-- Notification Container -->
    <div id="notification-container"></div>

    <div class="container mx-auto p-4 md:p-8 max-w-6xl">

        <!-- Enhanced Header -->
        <header class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 animate-fade-in-up">
            <div class="mb-4 md:mb-0">
                <h1 class="text-4xl font-bold text-black mb-2">Track Your Order</h1>
                <p class="text-gray-600">Real-time updates on your delivery</p>
            </div>

            <!-- Search Bar -->
            <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4 w-full md:w-auto">
                <div class="relative w-full md:w-64">
                    <input
                        type="text"
                        id="order-search"
                        placeholder="Search order ID..."
                        class="search-input w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
                    >
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"></i>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button id="notification-bell" class="relative p-2 hover:bg-gray-100 rounded-full transition-colors">
                            <i data-lucide="bell" class="w-6 h-6 text-black"></i>
                            <span id="notification-count" class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse-custom">2</span>
                        </button>
                    </div>
                    <button class="p-2 hover:bg-gray-100 rounded-full transition-colors">
                        <i data-lucide="user" class="w-6 h-6 text-black"></i>
                    </button>
                </div>
            </div>
        </header>

        <main class="grid grid-cols-1 lg:grid-cols-3 gap-8">

            <!-- Main Content: Left Column -->
            <div class="lg:col-span-2 space-y-8">

                <!-- Enhanced Order Details Card -->
                <div class="card p-6 rounded-lg shadow-sm animate-fade-in-up" style="animation-delay: 0.1s">
                    <div class="flex flex-col md:flex-row justify-between md:items-center gap-4">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-black rounded-full flex items-center justify-center">
                                <i data-lucide="package" class="w-6 h-6 text-white"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 text-sm">Order ID</p>
                                <p id="order-id" class="text-black font-semibold text-lg">#A45B-2024-N781</p>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="inline-block w-2 h-2 bg-green-500 rounded-full animate-pulse-custom"></span>
                                    <span class="text-sm text-green-600 font-medium">Active</span>
                                </div>
                            </div>
                        </div>
                        <div class="text-left md:text-right">
                            <p class="text-gray-500 text-sm">Estimated Delivery</p>
                            <p id="delivery-date" class="text-black font-semibold text-lg">June 9, 2025</p>
                            <p class="text-sm text-gray-600">2-3 business days</p>
                        </div>
                        <div class="flex flex-col space-y-2">
                            <button id="view-invoice-btn" class="btn-primary text-white font-semibold py-2 px-4 rounded-lg flex items-center justify-center gap-2">
                                <i data-lucide="file-text" class="w-4 h-4"></i>
                                View Invoice
                            </button>
                            <button id="view-items-btn" class="bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-blue-700 transition-colors">
                                <i data-lucide="package-open" class="w-4 h-4"></i>
                                View Items
                            </button>
                            <button id="share-tracking-btn" class="bg-white border border-black text-black font-semibold py-2 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-50 transition-colors">
                                <i data-lucide="share-2" class="w-4 h-4"></i>
                                Share
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Order Status Progress Bar -->
                <div id="order-status-tracker" class="card p-6 rounded-lg shadow-sm animate-fade-in-up" style="animation-delay: 0.2s">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-xl font-bold text-black">Order Progress</h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">Last updated:</span>
                            <span id="last-updated" class="text-sm font-medium text-black">15 mins ago</span>
                            <button id="refresh-status" class="p-1 hover:bg-gray-100 rounded transition-colors">
                                <i data-lucide="refresh-cw" class="w-4 h-4 text-gray-600"></i>
                            </button>
                        </div>
                    </div>

                    <div class="relative w-full">
                       <div class="progress-line"></div>
                       <div id="progress-line-active" class="progress-line progress-line-active" style="width: 75%;"></div>
                        <div class="flex justify-between items-start">
                            <!-- Step 1: Placed -->
                            <div class="step text-center w-1/5 cursor-pointer" data-status="placed" data-step="0">
                                <div class="icon-bg mx-auto w-14 h-14 rounded-full border-2 flex items-center justify-center bg-black border-black">
                                    <i data-lucide="package-check" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="font-semibold mt-3 text-sm">Placed</p>
                                <p class="text-xs text-gray-500 mt-1">Dec 5, 10:30 AM</p>
                            </div>
                             <!-- Step 2: Processed -->
                            <div class="step text-center w-1/5 cursor-pointer" data-status="processed" data-step="1">
                                <div class="icon-bg mx-auto w-14 h-14 rounded-full border-2 flex items-center justify-center bg-black border-black">
                                    <i data-lucide="settings-2" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="font-semibold mt-3 text-sm">Processed</p>
                                <p class="text-xs text-gray-500 mt-1">Dec 5, 2:15 PM</p>
                            </div>
                            <!-- Step 3: Shipped -->
                            <div class="step text-center w-1/5 cursor-pointer" data-status="shipped" data-step="2">
                               <div class="icon-bg mx-auto w-14 h-14 rounded-full border-2 flex items-center justify-center bg-black border-black">
                                    <i data-lucide="truck" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="font-semibold mt-3 text-sm">Shipped</p>
                                <p class="text-xs text-gray-500 mt-1">Dec 6, 9:00 AM</p>
                            </div>
                            <!-- Step 4: Out for Delivery -->
                             <div class="step text-center w-1/5 cursor-pointer" data-status="out-for-delivery" data-step="3">
                                <div class="icon-bg mx-auto w-14 h-14 rounded-full border-2 flex items-center justify-center bg-black border-black animate-pulse-custom">
                                    <i data-lucide="map-pin" class="w-6 h-6 text-white"></i>
                                </div>
                                <p class="font-semibold mt-3 text-sm">On The Way</p>
                                <p class="text-xs text-gray-500 mt-1">Dec 7, 8:45 AM</p>
                            </div>
                            <!-- Step 5: Delivered -->
                            <div class="step text-center w-1/5 cursor-pointer" data-status="delivered" data-step="4">
                                <div class="icon-bg mx-auto w-14 h-14 rounded-full border-2 flex items-center justify-center bg-white border-gray-300">
                                    <i data-lucide="home" class="w-6 h-6 text-gray-400"></i>
                                </div>
                                <p class="font-semibold mt-3 text-sm text-gray-400">Delivered</p>
                                <p class="text-xs text-gray-400 mt-1">Pending</p>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Details -->
                    <div id="step-details" class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                                <i data-lucide="map-pin" class="w-4 h-4 text-white"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-black">Out for Delivery</p>
                                <p class="text-sm text-gray-600">Your package is on its way and will be delivered today between 2:00 PM - 6:00 PM</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Map and Delivery Agent -->
                <div class="grid grid-cols-1 md:grid-cols-5 gap-8 animate-fade-in-up" style="animation-delay: 0.3s">
                    <div class="md:col-span-3 card rounded-lg shadow-sm overflow-hidden">
                        <!-- Enhanced Map with Controls -->
                        <div class="relative">
                            <div class="absolute top-4 left-4 z-[1000] flex space-x-2">
                                <button id="map-view-toggle" class="bg-white px-3 py-1 rounded-full text-sm font-medium shadow-md hover:shadow-lg transition-shadow">
                                    <i data-lucide="map" class="w-4 h-4 inline mr-1"></i>
                                    Street View
                                </button>
                                <button id="satellite-toggle" class="bg-white px-3 py-1 rounded-full text-sm font-medium shadow-md hover:shadow-lg transition-shadow">
                                    <i data-lucide="satellite" class="w-4 h-4 inline mr-1"></i>
                                    Satellite
                                </button>
                            </div>

                            <div class="absolute top-4 right-4 z-[1000]">
                                <button id="fullscreen-map" class="bg-white p-2 rounded-full shadow-md hover:shadow-lg transition-shadow">
                                    <i data-lucide="maximize" class="w-4 h-4"></i>
                                </button>
                            </div>

                            <!-- Interactive Map Container -->
                            <div id="delivery-map" class="map-container"></div>

                            <!-- ETA Banner -->
                            <div class="absolute bottom-4 left-4 right-4 bg-white rounded-lg p-3 shadow-lg z-[1000]">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse-custom"></div>
                                        <span id="eta-text" class="text-sm font-medium">ETA: 15-20 minutes</span>
                                    </div>
                                    <button id="track-live" class="text-blue-600 text-sm font-medium hover:underline">
                                        Track Live
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="md:col-span-2 flex flex-col gap-6">
                        <!-- Enhanced Delivery Agent -->
                        <div class="card p-6 rounded-lg shadow-sm flex-grow">
                             <div class="flex items-center justify-between mb-4">
                                 <h3 class="font-bold text-lg">Delivery Agent</h3>
                                 <div class="flex items-center space-x-1">
                                     <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                     <span class="text-xs text-green-600 font-medium">Online</span>
                                 </div>
                             </div>

                             <div class="flex items-center space-x-4 mb-4">
                                 <div class="relative">
                                     <div class="w-14 h-14 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                          <i data-lucide="user-circle" class="w-10 h-10 text-gray-600"></i>
                                     </div>
                                     <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                         <i data-lucide="check" class="w-3 h-3 text-white"></i>
                                     </div>
                                 </div>
                                 <div class="flex-1">
                                     <p class="font-semibold text-black">John Doe</p>
                                     <p class="text-sm text-gray-500">Authorized Agent</p>
                                     <div class="flex items-center space-x-1 mt-1">
                                         <div class="flex text-yellow-400">
                                             <i data-lucide="star" class="w-3 h-3 fill-current"></i>
                                             <i data-lucide="star" class="w-3 h-3 fill-current"></i>
                                             <i data-lucide="star" class="w-3 h-3 fill-current"></i>
                                             <i data-lucide="star" class="w-3 h-3 fill-current"></i>
                                             <i data-lucide="star" class="w-3 h-3 fill-current"></i>
                                         </div>
                                         <span class="text-xs text-gray-500">4.9 (127 reviews)</span>
                                     </div>
                                 </div>
                             </div>

                             <div class="grid grid-cols-2 gap-2">
                                 <button id="call-agent" class="bg-green-500 text-white font-semibold py-2 px-3 rounded-lg flex items-center justify-center gap-2 hover:bg-green-600 transition-colors text-sm">
                                     <i data-lucide="phone" class="w-4 h-4"></i>
                                     Call
                                 </button>
                                 <button id="chat-agent" class="bg-blue-500 text-white font-semibold py-2 px-3 rounded-lg flex items-center justify-center gap-2 hover:bg-blue-600 transition-colors text-sm">
                                     <i data-lucide="message-circle" class="w-4 h-4"></i>
                                     Chat
                                 </button>
                             </div>
                        </div>

                         <!-- Enhanced Proof of Delivery -->
                        <div class="dark-card p-6 rounded-lg shadow-sm flex-grow">
                             <h3 class="font-bold text-lg mb-4">Proof of Delivery</h3>
                             <div class="flex flex-col items-center justify-center h-full space-y-4">
                                 <div class="text-center">
                                      <i data-lucide="scan-line" class="w-12 h-12 text-white mx-auto mb-2"></i>
                                      <p class="text-sm text-gray-300 mb-2">Awaiting Delivery</p>
                                      <p class="text-xs text-gray-400">Photo and signature will appear here</p>
                                 </div>
                                 <button id="upload-proof" class="bg-white text-black px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                                     <i data-lucide="camera" class="w-4 h-4 inline mr-2"></i>
                                     Upload Photo
                                 </button>
                             </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Side Content: Right Column -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Enhanced Notifications/Updates -->
                <div class="card p-6 rounded-lg shadow-sm animate-fade-in-up" style="animation-delay: 0.4s">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-bold text-lg">Latest Updates</h3>
                        <button id="mark-all-read" class="text-xs text-blue-600 hover:underline">Mark all read</button>
                    </div>

                    <div id="updates-list" class="space-y-4 max-h-64 overflow-y-auto">
                        <div class="update-item flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer" data-read="false">
                            <div class="w-8 h-8 rounded-full bg-orange-500 flex-shrink-0 flex items-center justify-center">
                                <i data-lucide="map-pin" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-semibold text-sm">Your order is out for delivery.</p>
                                <p class="text-xs text-gray-500">15 mins ago</p>
                                <div class="w-2 h-2 bg-orange-500 rounded-full mt-1"></div>
                            </div>
                        </div>

                        <div class="update-item flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer" data-read="false" data-type="delay">
                            <div class="w-8 h-8 rounded-full bg-red-500 flex-shrink-0 flex items-center justify-center">
                                <i data-lucide="clock" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-semibold text-sm">Delivery delayed due to weather conditions.</p>
                                <p class="text-xs text-gray-500">1 hour ago</p>
                                <div class="flex space-x-2 mt-2">
                                    <button class="action-btn reschedule-btn">
                                        <i data-lucide="calendar" class="w-3 h-3 inline mr-1"></i>
                                        Reschedule
                                    </button>
                                    <button class="action-btn contact-support-btn">
                                        <i data-lucide="headphones" class="w-3 h-3 inline mr-1"></i>
                                        Contact Support
                                    </button>
                                </div>
                                <div class="w-2 h-2 bg-red-500 rounded-full mt-1"></div>
                            </div>
                        </div>

                        <div class="update-item flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer" data-read="true">
                            <div class="w-8 h-8 rounded-full bg-blue-500 flex-shrink-0 flex items-center justify-center">
                                <i data-lucide="truck" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-semibold text-sm">Package has been shipped from the hub.</p>
                                <p class="text-xs text-gray-500">2 hours ago</p>
                            </div>
                        </div>

                        <div class="update-item flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer" data-read="true">
                            <div class="w-8 h-8 rounded-full bg-green-500 flex-shrink-0 flex items-center justify-center">
                                <i data-lucide="settings-2" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-semibold text-sm">Order is being processed.</p>
                                <p class="text-xs text-gray-500">1 day ago</p>
                            </div>
                        </div>

                        <div class="update-item flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer" data-read="true">
                            <div class="w-8 h-8 rounded-full bg-purple-500 flex-shrink-0 flex items-center justify-center">
                                <i data-lucide="package-check" class="w-4 h-4 text-white"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-semibold text-sm">Order confirmed and payment received.</p>
                                <p class="text-xs text-gray-500">2 days ago</p>
                            </div>
                        </div>
                    </div>

                    <button id="view-all-updates" class="w-full mt-4 text-center text-sm text-blue-600 hover:underline">
                        View all updates
                    </button>
                </div>

                <!-- Enhanced Order History -->
                <div class="card p-6 rounded-lg shadow-sm animate-fade-in-up" style="animation-delay: 0.5s">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-bold text-lg">Order History</h3>
                        <button id="filter-orders" class="text-sm text-blue-600 hover:underline">
                            <i data-lucide="filter" class="w-4 h-4 inline mr-1"></i>
                            Filter
                        </button>
                    </div>

                    <div id="order-history-list" class="space-y-3 max-h-80 overflow-y-auto">
                        <div class="order-item order-item-clickable flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                             data-status="delivered"
                             data-order-id="#C12A-2024-N555"
                             data-delivery-date="May 28, 2025"
                             data-items='[{"name":"Wireless Headphones","image":"https://placehold.co/80x80/e2e8f0/333333?text=Headphones","quantity":1,"price":"$299.99"}]'>
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-sm">#C12A-2024-N555</p>
                                    <p class="text-xs text-gray-500">Delivered on May 28, 2025</p>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Electronics</span>
                                        <span class="text-xs text-gray-500">$299.99</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col space-y-1">
                                <button class="reorder-btn bg-black text-white text-xs font-bold py-1 px-3 rounded-full hover:bg-gray-800 transition-colors">Re-order</button>
                                <button class="text-xs text-blue-600 hover:underline">View Details</button>
                            </div>
                        </div>

                        <div class="order-item order-item-clickable flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                             data-status="delivered"
                             data-order-id="#F89B-2024-N431"
                             data-delivery-date="April 15, 2025"
                             data-items='[{"name":"Cotton T-Shirt","image":"https://placehold.co/80x80/e2e8f0/333333?text=T-Shirt","quantity":2,"price":"$89.99"}]'>
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-sm">#F89B-2024-N431</p>
                                    <p class="text-xs text-gray-500">Delivered on April 15, 2025</p>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Clothing</span>
                                        <span class="text-xs text-gray-500">$89.99</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col space-y-1">
                                <button class="reorder-btn bg-black text-white text-xs font-bold py-1 px-3 rounded-full hover:bg-gray-800 transition-colors">Re-order</button>
                                <button class="text-xs text-blue-600 hover:underline">View Details</button>
                            </div>
                        </div>

                        <div class="order-item order-item-clickable flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                             data-status="cancelled"
                             data-order-id="#D05E-2024-N102"
                             data-delivery-date="Cancelled on Mar 02, 2025"
                             data-items='[{"name":"Programming Book","image":"https://placehold.co/80x80/e2e8f0/333333?text=Book","quantity":1,"price":"$45.99"}]'>
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                                    <i data-lucide="x-circle" class="w-5 h-5 text-red-600"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-sm">#D05E-2024-N102</p>
                                    <p class="text-xs text-gray-500">Cancelled on Mar 02, 2025</p>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">Books</span>
                                        <span class="text-xs text-gray-500">$45.99</span>
                                    </div>
                                </div>
                            </div>
                            <button class="text-xs text-gray-500 hover:underline">View Details</button>
                        </div>
                    </div>

                    <button id="view-all-orders" class="w-full mt-4 text-center text-sm text-blue-600 hover:underline">
                        View all orders
                    </button>
                </div>

                <!-- Quick Actions Card -->
                <div class="card p-6 rounded-lg shadow-sm animate-fade-in-up" style="animation-delay: 0.6s">
                    <h3 class="font-bold text-lg mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <button id="report-issue" class="w-full flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            <i data-lucide="alert-circle" class="w-4 h-4 text-orange-500"></i>
                            <span class="text-sm font-medium">Report an Issue</span>
                        </button>
                        <button id="change-address" class="w-full flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            <i data-lucide="map-pin" class="w-4 h-4 text-blue-500"></i>
                            <span class="text-sm font-medium">Change Address</span>
                        </button>
                        <button id="reschedule-delivery" class="w-full flex items-center justify-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            <i data-lucide="calendar" class="w-4 h-4 text-green-500"></i>
                            <span class="text-sm font-medium">Reschedule Delivery</span>
                        </button>
                    </div>
                </div>

            </div>
        </main>

    </div>

    <!-- Modal for Order Items -->
    <div id="items-modal" class="modal">
        <div class="modal-content w-full max-w-2xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold">Order Items</h2>
                <button id="close-items-modal" class="text-gray-500 hover:text-gray-700">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <div id="items-list" class="space-y-4">
                <!-- Items will be populated here -->
            </div>
        </div>
    </div>

    <!-- Modal for Order History Details -->
    <div id="order-details-modal" class="modal">
        <div class="modal-content w-full max-w-4xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold">Order Details</h2>
                <button id="close-order-modal" class="text-gray-500 hover:text-gray-700">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <div id="order-details-content">
                <!-- Order details will be populated here -->
            </div>
        </div>
    </div>

    <script type="text/babel">
        // Enhanced Order Tracking Application with React Components and jQuery

        // Global state and configuration
        const AppState = {
            orderData: {
                id: '#A45B-2024-N781',
                estimatedDelivery: 'June 9, 2025',
                status: 'out-for-delivery',
                agent: {
                    name: 'John Doe',
                    rating: 4.9,
                    reviews: 127
                },
                proofOfDelivery: null,
                lastUpdated: '15 mins ago',
                items: [
                    {
                        name: 'Wireless Bluetooth Headphones',
                        image: 'https://placehold.co/120x120/e2e8f0/333333?text=Headphones',
                        quantity: 1,
                        price: '$299.99',
                        description: 'Premium noise-cancelling wireless headphones'
                    },
                    {
                        name: 'USB-C Charging Cable',
                        image: 'https://placehold.co/120x120/e2e8f0/333333?text=Cable',
                        quantity: 2,
                        price: '$19.99',
                        description: 'Fast charging USB-C cable 6ft'
                    }
                ]
            },
            theme: 'light',
            notifications: [],
            statusHierarchy: ['placed', 'processed', 'shipped', 'out-for-delivery', 'delivered'],
            map: null,
            deliveryMarker: null,
            destinationMarker: null,
            routeLine: null
        };

        // Utility Functions
        const Utils = {
            showNotification: function(message, type = 'info') {
                const notification = $(`
                    <div class="notification ${type}">
                        <div class="flex items-center space-x-2">
                            <i data-lucide="${type === 'success' ? 'check-circle' : 'info'}" class="w-5 h-5"></i>
                            <span>${message}</span>
                            <button class="ml-auto close-notification">
                                <i data-lucide="x" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                `);

                $('#notification-container').append(notification);
                lucide.createIcons();

                setTimeout(() => notification.addClass('show'), 100);
                setTimeout(() => {
                    notification.removeClass('show');
                    setTimeout(() => notification.remove(), 300);
                }, 5000);
            },

            formatTime: function(date) {
                return new Date(date).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            },

            animateCounter: function(element, start, end, duration) {
                const range = end - start;
                const increment = range / (duration / 16);
                let current = start;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= end) {
                        current = end;
                        clearInterval(timer);
                    }
                    $(element).text(Math.floor(current));
                }, 16);
            }
        };

        // Enhanced Progress Bar Update Function
        function updateProgressBar(currentStatus) {
            const steps = $('#order-status-tracker .step');
            const activeLine = $('#progress-line-active');

            const currentIndex = AppState.statusHierarchy.indexOf(currentStatus);
            if (currentIndex === -1) {
                console.error("Invalid order status:", currentStatus);
                return;
            }

            steps.each(function(index) {
                const $step = $(this);
                const $iconBg = $step.find('.icon-bg');
                const $icon = $iconBg.find('svg');
                const $label = $step.find('p').first();
                const $timestamp = $step.find('p').last();

                if (index <= currentIndex) {
                    $iconBg.removeClass('bg-white border-gray-300')
                           .addClass('bg-black border-black');
                    $icon.removeClass('text-gray-400').addClass('text-white');
                    $label.removeClass('text-gray-400');
                    $timestamp.removeClass('text-gray-400');

                    // Add completion animation
                    if (index < currentIndex) {
                        $iconBg.removeClass('animate-pulse-custom');
                    } else if (index === currentIndex) {
                        $iconBg.addClass('animate-pulse-custom');
                    }
                } else {
                    $iconBg.removeClass('bg-black border-black animate-pulse-custom')
                           .addClass('bg-white border-gray-300');
                    $icon.removeClass('text-white').addClass('text-gray-400');
                    $label.addClass('text-gray-400');
                    $timestamp.addClass('text-gray-400');
                }
            });

            // Animate progress line with proper timing
            const percentage = (currentIndex / (AppState.statusHierarchy.length - 1)) * 100;
            activeLine.css('width', `${percentage}%`);

            // Update step details
            updateStepDetails(currentStatus);
        }

        // Initialize Interactive Map
        function initializeMap() {
            // Default coordinates (New York City area)
            const deliveryLocation = [40.7589, -73.9851]; // Delivery agent location
            const destinationLocation = [40.7505, -73.9934]; // Customer location

            AppState.map = L.map('delivery-map').setView(deliveryLocation, 13);

            // Add tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(AppState.map);

            // Custom delivery truck icon
            const truckIcon = L.divIcon({
                html: '<div class="delivery-truck-moving"><i data-lucide="truck" class="w-6 h-6 text-blue-600"></i></div>',
                className: 'custom-div-icon',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            });

            // Custom destination icon
            const homeIcon = L.divIcon({
                html: '<i data-lucide="home" class="w-6 h-6 text-red-600"></i>',
                className: 'custom-div-icon',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            });

            // Add markers
            AppState.deliveryMarker = L.marker(deliveryLocation, { icon: truckIcon })
                .addTo(AppState.map)
                .bindPopup('Delivery Agent - John Doe<br>ETA: 15-20 minutes');

            AppState.destinationMarker = L.marker(destinationLocation, { icon: homeIcon })
                .addTo(AppState.map)
                .bindPopup('Your Delivery Address');

            // Add route line
            AppState.routeLine = L.polyline([deliveryLocation, destinationLocation], {
                color: 'blue',
                weight: 3,
                opacity: 0.7,
                dashArray: '10, 10'
            }).addTo(AppState.map);

            // Fit map to show both markers
            const group = new L.featureGroup([AppState.deliveryMarker, AppState.destinationMarker]);
            AppState.map.fitBounds(group.getBounds().pad(0.1));

            // Recreate icons after adding to map
            setTimeout(() => {
                lucide.createIcons();
            }, 100);
        }

        // Simulate real-time delivery tracking
        function simulateDeliveryMovement() {
            if (!AppState.deliveryMarker) return;

            const destination = [40.7505, -73.9934];
            const currentPos = AppState.deliveryMarker.getLatLng();

            // Calculate new position (move slightly towards destination)
            const newLat = currentPos.lat + (destination[0] - currentPos.lat) * 0.02;
            const newLng = currentPos.lng + (destination[1] - currentPos.lng) * 0.02;

            AppState.deliveryMarker.setLatLng([newLat, newLng]);

            // Update route line
            AppState.routeLine.setLatLngs([[newLat, newLng], destination]);

            // Update ETA based on distance
            const distance = AppState.map.distance([newLat, newLng], destination);
            const etaMinutes = Math.max(5, Math.round(distance / 100)); // Rough calculation
            $('#eta-text').text(`ETA: ${etaMinutes}-${etaMinutes + 5} minutes`);
        }

        function updateStepDetails(status) {
            const statusMessages = {
                'placed': 'Your order has been confirmed and is being prepared.',
                'processed': 'Your order is being processed and will be shipped soon.',
                'shipped': 'Your package has been shipped and is on its way.',
                'out-for-delivery': 'Your package is out for delivery and will arrive today between 2:00 PM - 6:00 PM.',
                'delivered': 'Your package has been successfully delivered.'
            };

            const statusIcons = {
                'placed': 'package-check',
                'processed': 'settings-2',
                'shipped': 'truck',
                'out-for-delivery': 'map-pin',
                'delivered': 'home'
            };

            $('#step-details').html(`
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                        <i data-lucide="${statusIcons[status]}" class="w-4 h-4 text-white"></i>
                    </div>
                    <div>
                        <p class="font-semibold text-black">${status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
                        <p class="text-sm text-gray-600">${statusMessages[status]}</p>
                    </div>
                </div>
            `);
            lucide.createIcons();
        }

        // Modal Functions
        function showItemsModal() {
            const items = AppState.orderData.items;
            let itemsHtml = '';

            items.forEach(item => {
                itemsHtml += `
                    <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                        <img src="${item.image}" alt="${item.name}" class="w-20 h-20 object-cover rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-semibold text-lg">${item.name}</h3>
                            <p class="text-gray-600 text-sm">${item.description}</p>
                            <div class="flex items-center justify-between mt-2">
                                <span class="text-sm text-gray-500">Quantity: ${item.quantity}</span>
                                <span class="font-semibold text-lg">${item.price}</span>
                            </div>
                        </div>
                    </div>
                `;
            });

            $('#items-list').html(itemsHtml);
            $('#items-modal').addClass('show');
        }

        function showOrderDetailsModal(orderData) {
            const items = JSON.parse(orderData.items);
            let itemsHtml = '';

            items.forEach(item => {
                itemsHtml += `
                    <div class="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg">
                        <img src="${item.image}" alt="${item.name}" class="w-16 h-16 object-cover rounded-lg">
                        <div class="flex-1">
                            <h4 class="font-semibold">${item.name}</h4>
                            <div class="flex items-center justify-between mt-1">
                                <span class="text-sm text-gray-500">Qty: ${item.quantity}</span>
                                <span class="font-semibold">${item.price}</span>
                            </div>
                        </div>
                    </div>
                `;
            });

            const content = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Order Information</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Order ID:</span>
                                <span class="font-semibold">${orderData.orderId}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Status:</span>
                                <span class="font-semibold capitalize">${orderData.status}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Date:</span>
                                <span class="font-semibold">${orderData.deliveryDate}</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold mb-4">Items Ordered</h3>
                        <div class="space-y-3">
                            ${itemsHtml}
                        </div>
                    </div>
                </div>
                <div class="mt-6 flex space-x-4">
                    <button class="load-order-btn bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Load This Order
                    </button>
                    <button class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        Download Invoice
                    </button>
                </div>
            `;

            $('#order-details-content').html(content);
            $('#order-details-modal').addClass('show');
        }

        function loadOrderData(orderData) {
            // Update main dashboard with selected order data
            $('#order-id').text(orderData.orderId);
            $('#delivery-date').text(orderData.deliveryDate);

            // Update progress bar based on status
            updateProgressBar(orderData.status);

            // Show success message
            Utils.showNotification(`Loaded order ${orderData.orderId}`, 'success');

            // Close modal
            $('#order-details-modal').removeClass('show');
        }

        // jQuery Event Handlers
        $(document).ready(function() {
            // Initialize Lucide Icons
            lucide.createIcons();

            // Initialize progress bar
            updateProgressBar(AppState.orderData.status);

            // Initialize interactive map
            setTimeout(() => {
                initializeMap();
                // Start delivery simulation
                setInterval(simulateDeliveryMovement, 5000);
            }, 1000);

            // Theme toggle functionality
            $('#theme-toggle').click(function() {
                AppState.theme = AppState.theme === 'light' ? 'dark' : 'light';
                $('body').toggleClass('dark-theme');

                const icon = AppState.theme === 'dark' ? 'sun' : 'moon';
                $(this).find('i').attr('data-lucide', icon);
                lucide.createIcons();

                Utils.showNotification(`Switched to ${AppState.theme} theme`, 'success');
            });

            // Search functionality
            $('#order-search').on('input', function() {
                const searchTerm = $(this).val().toLowerCase();
                if (searchTerm.length > 2) {
                    // Simulate search
                    setTimeout(() => {
                        if (searchTerm.includes('a45b')) {
                            Utils.showNotification('Order found!', 'success');
                        } else {
                            Utils.showNotification('No orders found matching your search.', 'info');
                        }
                    }, 500);
                }
            });

            // Notification bell
            $('#notification-bell').click(function() {
                const $count = $('#notification-count');
                const currentCount = parseInt($count.text());
                if (currentCount > 0) {
                    Utils.animateCounter($count[0], currentCount, 0, 500);
                    $count.removeClass('animate-pulse-custom');
                    Utils.showNotification('All notifications marked as read', 'success');
                }
            });

            // Progress step interactions
            $('.step').click(function() {
                const status = $(this).data('status');
                const step = $(this).data('step');

                Utils.showNotification(`Step ${step + 1}: ${status.replace('-', ' ').toUpperCase()}`, 'info');

                // Highlight clicked step
                $('.step').removeClass('scale-105');
                $(this).addClass('scale-105');
                setTimeout(() => $(this).removeClass('scale-105'), 200);
            });

            // Button interactions
            $('#view-invoice-btn').click(function() {
                $(this).addClass('animate-pulse-custom');
                setTimeout(() => {
                    $(this).removeClass('animate-pulse-custom');
                    Utils.showNotification('Invoice downloaded successfully!', 'success');
                }, 1000);
            });

            $('#view-items-btn').click(function() {
                showItemsModal();
            });

            $('#share-tracking-btn').click(function() {
                if (navigator.share) {
                    navigator.share({
                        title: 'Order Tracking',
                        text: `Track order ${AppState.orderData.id}`,
                        url: window.location.href
                    });
                } else {
                    // Fallback for browsers that don't support Web Share API
                    navigator.clipboard.writeText(window.location.href);
                    Utils.showNotification('Tracking link copied to clipboard!', 'success');
                }
            });

            // Modal close handlers
            $('#close-items-modal, #close-order-modal').click(function() {
                $('.modal').removeClass('show');
            });

            // Close modal when clicking outside
            $('.modal').click(function(e) {
                if (e.target === this) {
                    $(this).removeClass('show');
                }
            });

            // Delivery agent interactions
            $('#call-agent').click(function() {
                Utils.showNotification('Calling delivery agent...', 'info');
                // Simulate call
                setTimeout(() => {
                    Utils.showNotification('Call connected to John Doe', 'success');
                }, 2000);
            });

            $('#chat-agent').click(function() {
                Utils.showNotification('Opening chat with delivery agent...', 'info');
                // Here you would typically open a chat interface
            });

            // Map interactions
            $('#track-live').click(function() {
                if (AppState.deliveryMarker) {
                    AppState.deliveryMarker.openPopup();
                    Utils.showNotification('Live tracking activated', 'success');
                }
            });

            $('#map-view-toggle').click(function() {
                Utils.showNotification('Switched to street view', 'info');
            });

            $('#satellite-toggle').click(function() {
                Utils.showNotification('Satellite view (requires API key)', 'info');
            });

            $('#fullscreen-map').click(function() {
                if (AppState.map) {
                    AppState.map.toggleFullscreen();
                }
            });

            // Quick actions
            $('#report-issue').click(function() {
                Utils.showNotification('Issue report form opened', 'info');
            });

            $('#change-address').click(function() {
                Utils.showNotification('Address change form opened', 'info');
            });

            $('#reschedule-delivery').click(function() {
                Utils.showNotification('Delivery reschedule options opened', 'info');
            });

            // Order history interactions
            $('.reorder-btn').click(function(e) {
                e.stopPropagation();
                const orderId = $(this).closest('.order-item').find('.font-semibold').text();
                Utils.showNotification(`Reordering ${orderId}...`, 'success');
            });

            // Interactive order history - clicking on order items
            $('.order-item-clickable').click(function() {
                const orderData = {
                    orderId: $(this).data('order-id'),
                    status: $(this).data('status'),
                    deliveryDate: $(this).data('delivery-date'),
                    items: $(this).data('items')
                };
                showOrderDetailsModal(orderData);
            });

            // Load order button in modal
            $(document).on('click', '.load-order-btn', function() {
                const modal = $(this).closest('.modal');
                const orderData = {
                    orderId: modal.find('.font-semibold').first().text(),
                    status: 'delivered', // Past orders are delivered
                    deliveryDate: modal.find('.font-semibold').eq(2).text()
                };
                loadOrderData(orderData);
            });

            // Actionable notification buttons
            $(document).on('click', '.reschedule-btn', function(e) {
                e.stopPropagation();
                Utils.showNotification('Opening reschedule options...', 'info');
                // Here you would open a reschedule modal or form
            });

            $(document).on('click', '.contact-support-btn', function(e) {
                e.stopPropagation();
                Utils.showNotification('Connecting to support...', 'info');
                // Here you would open a support chat or form
            });

            // Update notifications
            $('.update-item').click(function() {
                const $this = $(this);
                if ($this.data('read') === false) {
                    $this.data('read', true);
                    $this.find('.w-2.h-2.bg-orange-500').fadeOut();
                }
            });

            // Mark all notifications as read
            $('#mark-all-read').click(function() {
                $('.update-item[data-read="false"]').each(function() {
                    $(this).data('read', true);
                    $(this).find('.w-2.h-2.bg-orange-500').fadeOut();
                });
                Utils.showNotification('All updates marked as read', 'success');
            });

            // Refresh status
            $('#refresh-status').click(function() {
                const $icon = $(this).find('i');
                $icon.addClass('animate-spin');

                setTimeout(() => {
                    $icon.removeClass('animate-spin');
                    $('#last-updated').text('Just now');
                    Utils.showNotification('Status refreshed', 'success');
                }, 1500);
            });

            // Close notification handler
            $(document).on('click', '.close-notification', function() {
                $(this).closest('.notification').removeClass('show');
                setTimeout(() => $(this).closest('.notification').remove(), 300);
            });

            // Auto-update simulation
            setInterval(() => {
                const now = new Date();
                $('#last-updated').text(Utils.formatTime(now));
            }, 60000); // Update every minute

            // Welcome message
            setTimeout(() => {
                Utils.showNotification('Welcome to Enhanced Order Tracking!', 'success');
            }, 1000);
        });

    </script>
</body>
</html>
